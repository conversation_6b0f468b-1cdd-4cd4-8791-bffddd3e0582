//! CS2 External Cheat - Overlay Window
//!
//! This module creates a transparent fullscreen overlay window using Wayland's layer shell protocol.
//! The overlay is designed to be used for displaying cheat information over the CS2 game window.
//!
//! IMPORTANT: This cheat is for educational purposes only and should only be used in private
//! games with bots and with the -insecure flag to disable VAC.
//!
//! The overlay uses:
//! - Layer shell protocol for positioning above all other windows
//! - Transparent rendering for see-through overlay
//! - No keyboard interactivity for click-through behavior
//! - Dynamic sizing to match screen dimensions

use std::num::NonZeroU32;
use std::thread;
use std::time::Duration;

mod drawing;
mod memory;
mod cs2;



use smithay_client_toolkit::{
    compositor::{CompositorHandler, CompositorState},
    delegate_compositor, delegate_layer, delegate_output, delegate_registry, delegate_shm,
    globals::GlobalData,
    output::{OutputHandler, OutputState},
    registry::{ProvidesRegistryState, RegistryState},
    registry_handlers,
    shell::{
        wlr_layer::{
            Anchor, KeyboardInteractivity, Layer, LayerShell, LayerShellHandler, LayerSurface,
            LayerSurfaceConfigure,
        },
        WaylandSurface,
    },
    shm::{slot::SlotPool, Shm, ShmHandler},
};
use wayland_client::{
    globals::registry_queue_init,
    protocol::{wl_compositor, wl_output, wl_region, wl_shm, wl_surface},
    Connection, QueueHandle,
};

/// Memory reading thread that continuously reads CS2 player positions
fn memory_thread() {
    println!("Starting CS2 memory reading thread...");

    // Try to find CS2 process
    let pid = match memory::MemoryReader::find_cs2_process() {
        Some(pid) => {
            println!("Found CS2 process with PID: {}", pid);
            pid
        }
        None => {
            println!("CS2 process not found! Make sure CS2 is running.");
            return;
        }
    };

    // Create CS2 memory reader
    let mut cs2_memory = match cs2::CS2Memory::new(pid) {
        Ok(memory) => memory,
        Err(e) => {
            println!("Failed to create CS2 memory reader: {}", e);
            println!("Note: You may need to run this program with sudo for memory access permissions.");
            println!("Try: sudo ./target/debug/cs2-external-learning");
            return;
        }
    };

    println!("CS2 memory reader initialized successfully!");
    println!("Reading player positions every 1 second...");
    println!("Press Ctrl+C to stop.\n");

    // Main memory reading loop
    loop {
        match read_and_print_players(&mut cs2_memory) {
            Ok(_) => {},
            Err(e) => {
                println!("Error reading memory: {}", e);
            }
        }

        // Sleep for 1 second before next read
        thread::sleep(Duration::from_secs(1));
    }
}

/// Read and print all player positions
fn read_and_print_players(cs2_memory: &mut cs2::CS2Memory) -> Result<(), Box<dyn std::error::Error>> {
    // Get local player first
    match cs2_memory.get_local_player()? {
        Some(local_player) => {
            println!("=== LOCAL PLAYER ===");
            println!("Name: {}", local_player.name);
            println!("Health: {}", local_player.health);
            println!("Team: {}", local_player.team);
            println!("Position: {}", local_player.position);
            println!();
        }
        None => {
            println!("Local player not found or not in game");
            return Ok(());
        }
    }

    // Get all players
    let players = cs2_memory.get_all_players()?;

    if players.is_empty() {
        println!("No other players found");
        return Ok(());
    }

    println!("=== OTHER PLAYERS ===");
    for (i, player) in players.iter().enumerate() {
        println!("Player {}: {}", i + 1, player.name);
        println!("  Health: {}", player.health);
        println!("  Team: {}", player.team);
        println!("  Position: {}", player.position);
    }
    println!("Found {} other players\n", players.len());

    Ok(())
}

fn main() {
    env_logger::init();

    // Start memory reading thread
    thread::spawn(|| {
        memory_thread();
    });

    // Try to connect to Wayland compositor, but handle failure gracefully
    let conn = match Connection::connect_to_env() {
        Ok(conn) => conn,
        Err(e) => {
            println!("Failed to connect to Wayland compositor: {}. Running in memory-only mode.", e);
            // Just run the memory thread and wait
            loop {
                thread::sleep(std::time::Duration::from_secs(1));
            }
        }
    };

    // Enumerate the list of globals to get the protocols the server implements.
    let (globals, mut event_queue) = registry_queue_init(&conn).unwrap();
    let qh = event_queue.handle();

    // The compositor (not to be confused with the server which is commonly called the compositor) allows
    // configuring surfaces to be presented.
    let compositor = CompositorState::bind(&globals, &qh).expect("wl_compositor is not available");

    // This app uses the wlr layer shell, which may not be available with every compositor.
    let layer_shell = LayerShell::bind(&globals, &qh).expect("layer shell is not available");

    // Since we are not using the GPU in this example, we use wl_shm to allow software rendering to a buffer
    // we share with the compositor process.
    let shm = Shm::bind(&globals, &qh).expect("wl_shm is not available");

    // Get the raw compositor for creating input regions
    let wl_compositor = globals.bind::<wl_compositor::WlCompositor, _, _>(&qh, 1..=6, GlobalData).unwrap();

    // A layer surface is created from a surface.
    let surface = compositor.create_surface(&qh);

    // And then we create the layer shell.
    let layer = layer_shell.create_layer_surface(&qh, surface, Layer::Overlay, Some("cs2_overlay"), None);

    // Configure the layer surface for a fullscreen overlay
    // - Anchor to all edges to cover the entire screen
    // - No keyboard interactivity to allow click-through
    // - Set to overlay layer for highest z-order
    // - Don't reserve compositor space
    layer.set_anchor(Anchor::TOP | Anchor::BOTTOM | Anchor::LEFT | Anchor::RIGHT);
    layer.set_keyboard_interactivity(KeyboardInteractivity::None);
    layer.set_size(0, 0); // 0,0 means use the full output size
    layer.set_exclusive_zone(-1); // Don't reserve compositor space

    // Input region will be set up in the configure handler for proper click-through behavior

    // In order for the layer surface to be mapped, we need to perform an initial commit with no attached
    // buffer. For more info, see WaylandSurface::commit
    //
    // The compositor will respond with an initial configure that we can then use to present to the layer
    // surface with the correct options.
    layer.commit();

    // We don't know how large the window will be yet, so lets assume a reasonable size for the
    // initial memory allocation. This will be resized when we get the actual screen dimensions.
    let pool = SlotPool::new(1920 * 1080 * 4, &shm).expect("Failed to create pool");

    let mut cs2_overlay = CS2Overlay {
        registry_state: RegistryState::new(&globals),
        output_state: OutputState::new(&globals, &qh),
        shm,
        exit: false,
        first_configure: true,
        pool,
        width: 1920,
        height: 1080,
        layer,
        compositor,
        wl_compositor,
        input_region: None,
    };

    // We don't draw immediately, the configure will notify us when to first draw.
    loop {
        event_queue.blocking_dispatch(&mut cs2_overlay).unwrap();

        if cs2_overlay.exit {
            break;
        }
    }
}

/// CS2 External Cheat Overlay
///
/// This struct represents the main overlay window for the CS2 external cheat.
/// It creates a transparent fullscreen overlay using Wayland's layer shell protocol.
/// The overlay is positioned on the highest layer (Overlay) and is configured to be
/// click-through and non-interactive.
struct CS2Overlay {
    registry_state: RegistryState,
    output_state: OutputState,
    shm: Shm,
    exit: bool,
    first_configure: bool,
    pool: SlotPool,
    width: u32,
    height: u32,
    layer: LayerSurface,
    compositor: CompositorState,
    wl_compositor: wl_compositor::WlCompositor,
    input_region: Option<wl_region::WlRegion>,
}

impl CompositorHandler for CS2Overlay {
    fn scale_factor_changed(
        &mut self,
        _conn: &Connection,
        _qh: &QueueHandle<Self>,
        _surface: &wl_surface::WlSurface,
        _new_factor: i32,
    ) {
        // Not needed for this example.
    }

    fn transform_changed(
        &mut self,
        _conn: &Connection,
        _qh: &QueueHandle<Self>,
        _surface: &wl_surface::WlSurface,
        _new_transform: wl_output::Transform,
    ) {
        // Not needed for this example.
    }

    fn frame(
        &mut self,
        _conn: &Connection,
        qh: &QueueHandle<Self>,
        _surface: &wl_surface::WlSurface,
        _time: u32,
    ) {
        self.draw(qh);
    }
}

impl OutputHandler for CS2Overlay {
    fn output_state(&mut self) -> &mut OutputState {
        &mut self.output_state
    }

    fn new_output(
        &mut self,
        _conn: &Connection,
        _qh: &QueueHandle<Self>,
        _output: wl_output::WlOutput,
    ) {
    }

    fn update_output(
        &mut self,
        _conn: &Connection,
        _qh: &QueueHandle<Self>,
        _output: wl_output::WlOutput,
    ) {
    }

    fn output_destroyed(
        &mut self,
        _conn: &Connection,
        _qh: &QueueHandle<Self>,
        _output: wl_output::WlOutput,
    ) {
    }
}

impl LayerShellHandler for CS2Overlay {
    fn closed(&mut self, _conn: &Connection, _qh: &QueueHandle<Self>, _layer: &LayerSurface) {
        self.exit = true;
    }

    fn configure(
        &mut self,
        _conn: &Connection,
        qh: &QueueHandle<Self>,
        _layer: &LayerSurface,
        configure: LayerSurfaceConfigure,
        _serial: u32,
    ) {
        // Update dimensions based on compositor's response
        // If the compositor doesn't specify a size, use reasonable defaults
        self.width = NonZeroU32::new(configure.new_size.0).map_or(1920, NonZeroU32::get);
        self.height = NonZeroU32::new(configure.new_size.1).map_or(1080, NonZeroU32::get);

        // Initiate the first draw after initial configuration
        if self.first_configure {
            self.first_configure = false;
            // Set up click-through behavior
            self.setup_click_through(qh);
            self.draw(qh);
        }
    }
}

impl ShmHandler for CS2Overlay {
    fn shm_state(&mut self) -> &mut Shm {
        &mut self.shm
    }
}

impl CS2Overlay {
    /// Create and set an empty input region to make the overlay click-through
    pub fn setup_click_through(&mut self, qh: &QueueHandle<Self>) {
        // Create an empty input region using the raw compositor
        let region = self.wl_compositor.create_region(qh, ());
        // NOTE: We don't add any rectangles to the region, making it empty
        // This means no input events will be captured by this surface

        // Set the empty input region on the layer surface
        self.layer.wl_surface().set_input_region(Some(&region));

        // Store the region so we can manage its lifetime
        self.input_region = Some(region);

        // Commit the changes
        self.layer.commit();
    }

    pub fn draw(&mut self, qh: &QueueHandle<Self>) {
        let width = self.width;
        let height = self.height;
        let stride = self.width as i32 * 4;

        // Ensure the pool is large enough for the current dimensions
        let required_size = (width * height * 4) as usize;
        if self.pool.len() < required_size {
            self.pool = SlotPool::new(required_size, &self.shm).expect("Failed to resize pool");
        }

        let (buffer, canvas) = self
            .pool
            .create_buffer(width as i32, height as i32, stride, wl_shm::Format::Argb8888)
            .expect("create buffer");

        // Create a fully transparent overlay and optionally add cheat graphics
        // For demonstration, we'll draw a simple crosshair (uncomment to enable)

        // Clear with transparent background
        canvas.chunks_exact_mut(4).for_each(|chunk| {
            let array: &mut [u8; 4] = chunk.try_into().unwrap();
            *array = [0, 0, 0, 0]; // Fully transparent (B, G, R, A)
        });

        // Example: Draw a crosshair (uncomment the lines below to enable)
        
        let mut ctx = drawing::DrawingContext::new(canvas, width, height);
        ctx.draw_crosshair(20, 2, drawing::Color::white(128));

        // Example: Draw an ESP box
        let esp_box = drawing::Rect::new(100, 100, 50, 100);
        ctx.draw_esp_box(esp_box, drawing::Color::red(180), 2);
        

        // Damage the entire window to indicate it needs to be redrawn
        self.layer.wl_surface().damage_buffer(0, 0, width as i32, height as i32);

        // Request our next frame callback
        self.layer.wl_surface().frame(qh, self.layer.wl_surface().clone());

        // Attach the buffer and commit to present the frame
        buffer.attach_to(self.layer.wl_surface()).expect("buffer attach");
        self.layer.commit();
    }
}

// Delegate implementations
delegate_compositor!(CS2Overlay);
delegate_output!(CS2Overlay);
delegate_shm!(CS2Overlay);
delegate_layer!(CS2Overlay);
delegate_registry!(CS2Overlay);

impl ProvidesRegistryState for CS2Overlay {
    fn registry(&mut self) -> &mut RegistryState {
        &mut self.registry_state
    }
    registry_handlers![OutputState];
}

// Simple dispatch implementation for WlRegion
impl wayland_client::Dispatch<wl_region::WlRegion, ()> for CS2Overlay {
    fn event(
        _state: &mut Self,
        _proxy: &wl_region::WlRegion,
        _event: <wl_region::WlRegion as wayland_client::Proxy>::Event,
        _data: &(),
        _conn: &Connection,
        _qhandle: &QueueHandle<Self>,
    ) {
        // Regions don't have events we need to handle
    }
}