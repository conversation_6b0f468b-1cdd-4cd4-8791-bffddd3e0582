use crate::pattern_scanner::{Pat<PERSON>Scanner, LinuxPatterns};

/// Osiris-style memory reader with dynamic pattern scanning
pub struct OsirisMemoryReader {
    pid: u32,
    scanner: PatternScanner,
    client_base: usize,
    
    // Dynamically discovered addresses and offsets
    entity_system_ptr: Option<usize>,
    highest_entity_index_offset: Option<usize>,
    entity_list_offset: Option<usize>,
    offset_to_game_scene_node: Option<usize>,
    offset_to_health: Option<usize>,
    offset_to_team_number: Option<usize>,
}

/// Osiris-style entity system
pub struct OsirisEntitySystem {
    memory: OsirisMemoryReader,
    entity_system_address: usize,
    highest_entity_index_offset: usize,
    entity_list_offset: usize,
}

/// Player data structure
#[derive(Debug, <PERSON><PERSON>)]
pub struct OsirisPlayer {
    pub entity_index: i32,
    pub entity_address: usize,
    pub health: i32,
    pub team: u8,
    pub position: Vector3,
    pub name: String,
}

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct Vector3 {
    pub x: f32,
    pub y: f32,
    pub z: f32,
}

impl Vector3 {
    pub fn zero() -> Self {
        Self { x: 0.0, y: 0.0, z: 0.0 }
    }
}

impl OsirisMemoryReader {
    /// Create a new Osiris-style memory reader
    pub fn new(pid: u32, client_base: usize) -> Result<Self, Box<dyn std::error::Error>> {
        let scanner = PatternScanner::new(pid)?;
        
        Ok(Self {
            pid,
            scanner,
            client_base,
            entity_system_ptr: None,
            highest_entity_index_offset: None,
            entity_list_offset: None,
            offset_to_game_scene_node: None,
            offset_to_health: None,
            offset_to_team_number: None,
        })
    }
    
    /// Initialize by scanning for all required patterns
    pub fn initialize(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        println!("Initializing Osiris memory reader with dynamic pattern scanning...");
        
        // Scan for EntitySystemPointer
        println!("Scanning for EntitySystemPointer...");
        self.entity_system_ptr = Some(LinuxPatterns::entity_system_pointer()
            .resolve(self.client_base, &mut self.scanner)?);
        println!("EntitySystemPointer: 0x{:X}", self.entity_system_ptr.unwrap());
        
        // Scan for HighestEntityIndexOffset
        println!("Scanning for HighestEntityIndexOffset...");
        self.highest_entity_index_offset = Some(LinuxPatterns::highest_entity_index_offset()
            .resolve(self.client_base, &mut self.scanner)?);
        println!("HighestEntityIndexOffset: 0x{:X}", self.highest_entity_index_offset.unwrap());
        
        // Scan for EntityListOffset - try multiple patterns
        println!("Scanning for EntityListOffset...");
        self.entity_list_offset = match LinuxPatterns::entity_list_offset()
            .resolve(self.client_base, &mut self.scanner) {
            Ok(offset) => {
                println!("EntityListOffset (primary): 0x{:X}", offset);
                Some(offset)
            }
            Err(_) => {
                println!("Primary EntityListOffset pattern failed, trying alternative...");
                match LinuxPatterns::entity_list_offset_alt()
                    .resolve(self.client_base, &mut self.scanner) {
                    Ok(offset) => {
                        println!("EntityListOffset (alt1): 0x{:X}", offset);
                        Some(offset)
                    }
                    Err(_) => {
                        println!("Alternative 1 failed, trying alternative 2...");
                        match LinuxPatterns::entity_list_offset_alt2()
                            .resolve(self.client_base, &mut self.scanner) {
                            Ok(offset) => {
                                println!("EntityListOffset (alt2): 0x{:X}", offset);
                                Some(offset)
                            }
                            Err(e) => {
                                println!("All EntityListOffset patterns failed: {}", e);
                                // For now, use a fallback offset based on common entity system layouts
                                println!("Using fallback EntityListOffset: 0x10");
                                Some(0x10)
                            }
                        }
                    }
                }
            }
        };
        
        // Scan for OffsetToGameSceneNode
        println!("Scanning for OffsetToGameSceneNode...");
        self.offset_to_game_scene_node = Some(LinuxPatterns::offset_to_game_scene_node()
            .resolve(self.client_base, &mut self.scanner)?);
        println!("OffsetToGameSceneNode: 0x{:X}", self.offset_to_game_scene_node.unwrap());
        
        // Scan for OffsetToHealth
        println!("Scanning for OffsetToHealth...");
        self.offset_to_health = Some(LinuxPatterns::offset_to_health()
            .resolve(self.client_base, &mut self.scanner)?);
        println!("OffsetToHealth: 0x{:X}", self.offset_to_health.unwrap());
        
        // Scan for OffsetToTeamNumber
        println!("Scanning for OffsetToTeamNumber...");
        self.offset_to_team_number = Some(LinuxPatterns::offset_to_team_number()
            .resolve(self.client_base, &mut self.scanner)?);
        println!("OffsetToTeamNumber: 0x{:X}", self.offset_to_team_number.unwrap());
        
        println!("Osiris memory reader initialized successfully!");
        Ok(())
    }
    
    /// Read a value from memory
    pub fn read<T>(&mut self, address: usize) -> Result<T, Box<dyn std::error::Error>>
    where
        T: Copy + Default,
    {
        self.scanner.read_at(address)
    }
    
    /// Get the entity system address
    pub fn get_entity_system_address(&self) -> Result<usize, Box<dyn std::error::Error>> {
        match self.entity_system_ptr {
            Some(ptr) => {
                // Read the actual entity system address from the pointer
                let mut scanner = PatternScanner::new(self.pid)?;
                scanner.read_at(ptr)
            }
            None => Err("EntitySystemPointer not initialized".into()),
        }
    }
    
    /// Get offsets
    pub fn get_highest_entity_index_offset(&self) -> usize {
        self.highest_entity_index_offset.unwrap_or(0)
    }
    
    pub fn get_entity_list_offset(&self) -> usize {
        self.entity_list_offset.unwrap_or(0)
    }
    
    pub fn get_offset_to_game_scene_node(&self) -> usize {
        self.offset_to_game_scene_node.unwrap_or(0)
    }
    
    pub fn get_offset_to_health(&self) -> usize {
        self.offset_to_health.unwrap_or(0)
    }
    
    pub fn get_offset_to_team_number(&self) -> usize {
        self.offset_to_team_number.unwrap_or(0)
    }
}

impl OsirisEntitySystem {
    /// Create a new Osiris entity system
    pub fn new(mut memory: OsirisMemoryReader) -> Result<Self, Box<dyn std::error::Error>> {
        // Initialize pattern scanning
        memory.initialize()?;
        
        // Get the entity system address
        let entity_system_address = memory.get_entity_system_address()?;
        println!("Entity System Address: 0x{:X}", entity_system_address);
        
        let highest_entity_index_offset = memory.get_highest_entity_index_offset();
        let entity_list_offset = memory.get_entity_list_offset();
        
        Ok(Self {
            memory,
            entity_system_address,
            highest_entity_index_offset,
            entity_list_offset,
        })
    }
    
    /// Get the highest entity index
    pub fn get_highest_entity_index(&mut self) -> Result<i32, Box<dyn std::error::Error>> {
        let address = self.entity_system_address + self.highest_entity_index_offset;
        self.memory.read::<i32>(address)
    }
    
    /// Get entity by index using Osiris method
    pub fn get_entity_by_index(&mut self, index: i32) -> Result<Option<usize>, Box<dyn std::error::Error>> {
        if index < 0 || index >= 0x8000 {
            return Ok(None);
        }
        
        // Calculate chunk and index within chunk
        let chunk_index = index / 512;
        let index_in_chunk = index % 512;
        
        // Get the entity list
        let entity_list_address = self.entity_system_address + self.entity_list_offset;
        
        // Get the chunk pointer
        let chunk_ptr_address = entity_list_address + (chunk_index as usize * 8);
        let chunk_ptr: usize = self.memory.read(chunk_ptr_address)?;
        
        if chunk_ptr == 0 || chunk_ptr < 0x1000 {
            return Ok(None);
        }
        
        // Get the entity identity
        let identity_address = chunk_ptr + (index_in_chunk as usize * 120); // 120 bytes per entity identity
        let entity_ptr: usize = self.memory.read(identity_address)?;
        
        if entity_ptr == 0 || entity_ptr < 0x1000 {
            return Ok(None);
        }
        
        Ok(Some(entity_ptr))
    }
    
    /// Get player data from entity
    pub fn get_player_from_entity(&mut self, entity_ptr: usize, index: i32) -> Result<Option<OsirisPlayer>, Box<dyn std::error::Error>> {
        // Read health using dynamically discovered offset
        let health_offset = self.memory.get_offset_to_health();
        let health: i32 = self.memory.read(entity_ptr + health_offset)?;
        
        // Only process entities with valid health
        if health <= 0 || health > 100 {
            return Ok(None);
        }
        
        // Read team using dynamically discovered offset
        let team_offset = self.memory.get_offset_to_team_number();
        let team: u8 = self.memory.read(entity_ptr + team_offset)?;
        
        // Read position from GameSceneNode
        let position = self.get_entity_position(entity_ptr)?;
        
        Ok(Some(OsirisPlayer {
            entity_index: index,
            entity_address: entity_ptr,
            health,
            team,
            position,
            name: format!("Player{}", index),
        }))
    }
    
    /// Get entity position from GameSceneNode using AbsOrigin
    fn get_entity_position(&mut self, entity_ptr: usize) -> Result<Vector3, Box<dyn std::error::Error>> {
        let game_scene_node_offset = self.memory.get_offset_to_game_scene_node();
        let game_scene_node_ptr: usize = self.memory.read(entity_ptr + game_scene_node_offset)?;

        if game_scene_node_ptr == 0 || game_scene_node_ptr < 0x1000 {
            return Ok(Vector3::zero());
        }

        // Try to get AbsOrigin offset dynamically (this would require another pattern scan)
        // For now, use a common offset for AbsOrigin in GameSceneNode
        let abs_origin_offset = 0x80; // This is a fallback, should be dynamically discovered
        let x: f32 = self.memory.read(game_scene_node_ptr + abs_origin_offset)?;
        let y: f32 = self.memory.read(game_scene_node_ptr + abs_origin_offset + 4)?;
        let z: f32 = self.memory.read(game_scene_node_ptr + abs_origin_offset + 8)?;

        Ok(Vector3 { x, y, z })
    }
    
    /// Find all players using Osiris method
    pub fn find_all_players(&mut self) -> Result<Vec<OsirisPlayer>, Box<dyn std::error::Error>> {
        let mut players = Vec::new();
        
        let highest_index = self.get_highest_entity_index()?;
        println!("Highest entity index: {}", highest_index);
        
        // Scan through all entities
        for index in 0..=highest_index.min(2047) { // Limit to reasonable range
            if let Some(entity_ptr) = self.get_entity_by_index(index)? {
                if let Some(player) = self.get_player_from_entity(entity_ptr, index)? {
                    println!("Found player at index {}: health={}, team={}, pos=({:.2}, {:.2}, {:.2})", 
                             index, player.health, player.team, player.position.x, player.position.y, player.position.z);
                    players.push(player);
                }
            }
        }
        
        Ok(players)
    }
}
