use crate::memory::{<PERSON><PERSON><PERSON><PERSON>, Player, Vector3, EntitySystem, offsets};

/// CS2 Memory Manager
pub struct CS2Memory {
    memory: MemoryReader,
    client_base: usize,
    entity_system: EntitySystem,
}

impl CS2Memory {
    /// Create a new CS2 memory manager
    pub fn new(pid: u32) -> Result<Self, Box<dyn std::error::Error>> {
        let memory = MemoryReader::new(pid)?;

        // Find the client.so base address
        let client_base = Self::find_client_base(pid)?;
        println!("Found client.so base address: 0x{:X}", client_base);

        // Initialize the entity system
        let entity_system = EntitySystem::new(MemoryReader::new(pid)?, client_base)?;

        Ok(CS2Memory {
            memory,
            client_base,
            entity_system,
        })
    }

    /// Find the base address of client.so module
    fn find_client_base(pid: u32) -> Result<usize, Box<dyn std::error::Error>> {
        use std::fs::File;
        use std::io::{<PERSON><PERSON><PERSON><PERSON>, <PERSON>uf<PERSON>ead<PERSON>};

        let maps_path = format!("/proc/{}/maps", pid);
        let file = File::open(maps_path)?;
        let reader = BufReader::new(file);

        for line in reader.lines() {
            let line = line?;
            if line.contains("libclient.so") {
                // Parse the base address from the line
                // Format: "address_start-address_end permissions offset device inode pathname"
                let parts: Vec<&str> = line.split_whitespace().collect();
                if let Some(addr_range) = parts.first() {
                    let addr_start = addr_range.split('-').next().unwrap_or("0");
                    let base_addr = usize::from_str_radix(addr_start, 16)?;
                    return Ok(base_addr);
                }
            }
        }

        Err("Could not find libclient.so in process memory maps".into())
    }

    /// Get the local player controller
    pub fn get_local_player_controller(&mut self) -> Result<usize, Box<dyn std::error::Error>> {
        let address = self.client_base + offsets::DW_LOCAL_PLAYER_CONTROLLER;
        let controller = self.memory.read::<usize>(address)?;
        println!("Local player controller address: 0x{:X} -> 0x{:X}", address, controller);
        Ok(controller)
    }

    /// Get the local player pawn
    pub fn get_local_player_pawn(&mut self) -> Result<usize, Box<dyn std::error::Error>> {
        let address = self.client_base + offsets::DW_LOCAL_PLAYER_PAWN;
        self.memory.read::<usize>(address)
    }

    /// Get player pawn from controller (using deadlocked method)
    pub fn get_player_pawn(&mut self, controller: usize) -> Result<usize, Box<dyn std::error::Error>> {
        if controller == 0 {
            println!("  Controller is null");
            return Ok(0);
        }

        println!("  Reading pawn handle from controller 0x{:X} + 0x{:X}", controller, offsets::M_H_PLAYER_PAWN);
        let pawn_handle: i32 = self.memory.read(controller + offsets::M_H_PLAYER_PAWN)?;
        println!("  Pawn handle: 0x{:X}", pawn_handle);

        if pawn_handle == -1 {
            println!("  Invalid pawn handle");
            return Ok(0);
        }

        // Debug the entity list structure
        let entity_index = (pawn_handle & 0x7FFF) as usize;
        println!("  Entity index: {}", entity_index);
        println!("  Entity index >> 9: {}", entity_index >> 9);
        println!("  Entity index & 0x1FF: {}", entity_index & 0x1FF);

        let entity_list_base = self.client_base + offsets::DW_ENTITY_LIST;
        println!("  Entity list base: 0x{:X}", entity_list_base);

        // Let's try to read some values from the entity list to see what's there
        for i in 0..4 {
            let test_addr = entity_list_base + i * 8;
            let test_val: usize = self.memory.read(test_addr).unwrap_or(0);
            println!("  EntityList[{}] (0x{:X}): 0x{:X}", i, test_addr, test_val);
        }

        // Try the original method from our first implementation
        println!("  Trying original method...");
        let entity_entry = entity_list_base + ((entity_index >> 9) * 8);
        println!("  Reading entity chunk from 0x{:X}", entity_entry);
        let entity_chunk: usize = self.memory.read(entity_entry).unwrap_or(0);
        println!("  Entity chunk: 0x{:X}", entity_chunk);

        if entity_chunk != 0 {
            let entity_address = entity_chunk + ((entity_index & 0x1FF) * 120);
            println!("  Entity address: 0x{:X}", entity_address);

            // Read the first few values from the entity to see what's there
            for i in 0..8 {
                let offset = i * 8;
                let value: usize = self.memory.read(entity_address + offset).unwrap_or(0);
                println!("    Entity[{}] (+0x{:X}): 0x{:X}", i, offset, value);
            }

            // Try reading from different offsets in case the pawn is not at offset 0
            for offset in [0, 8, 16, 24, 32, 40, 48, 56] {
                let pawn: usize = self.memory.read(entity_address + offset).unwrap_or(0);
                if pawn != 0 && pawn > 0x1000 && pawn < 0x7FFFFFFFFFFF {
                    // Test if this looks like a valid pawn by trying to read health
                    let test_health: i32 = self.memory.read(pawn + offsets::M_I_HEALTH).unwrap_or(-1);
                    if test_health >= 0 && test_health <= 100 {
                        println!("  Found valid pawn at offset 0x{:X}: 0x{:X} (health: {})", offset, pawn, test_health);
                        return Ok(pawn);
                    }
                }
            }
        }

        // Try different entity sizes
        println!("  Trying different entity sizes...");
        for entity_size in [120, 128, 136, 144, 152, 160] {
            if entity_chunk != 0 {
                let entity_address = entity_chunk + ((entity_index & 0x1FF) * entity_size);
                let pawn: usize = self.memory.read(entity_address).unwrap_or(0);
                if pawn != 0 && pawn > 0x1000 && pawn < 0x7FFFFFFFFFFF {
                    println!("  Found potential pawn with size {}: 0x{:X}", entity_size, pawn);
                    return Ok(pawn);
                }
            }
        }

        println!("  All methods failed");
        Ok(0)
    }

    /// Get player position from pawn
    pub fn get_player_position(&mut self, pawn: usize) -> Result<Vector3, Box<dyn std::error::Error>> {
        if pawn == 0 {
            return Ok(Vector3::zero());
        }

        println!("    Reading GameSceneNode from pawn 0x{:X} + 0x{:X}", pawn, offsets::M_P_GAME_SCENE_NODE);
        let game_scene_node: usize = self.memory.read(pawn + offsets::M_P_GAME_SCENE_NODE)?;
        println!("    GameSceneNode: 0x{:X}", game_scene_node);

        if game_scene_node == 0 {
            println!("    GameSceneNode is null");
            return Ok(Vector3::zero());
        }

        // Try reading from different offsets to find the position
        for offset in [0x88, 0xD0, 0x80, 0x90, 0xA0, 0xB0, 0xC0] {
            let position: Vector3 = self.memory.read(game_scene_node + offset).unwrap_or(Vector3::zero());
            if position.x != 0.0 || position.y != 0.0 || position.z != 0.0 {
                println!("    Found non-zero position at offset 0x{:X}: {}", offset, position);
                return Ok(position);
            }
        }

        println!("    All position offsets returned zero");
        Ok(Vector3::zero())
    }

    /// Get player health
    pub fn get_player_health(&mut self, pawn: usize) -> Result<i32, Box<dyn std::error::Error>> {
        if pawn == 0 {
            return Ok(0);
        }

        let health_addr = pawn + offsets::M_I_HEALTH;
        println!("    Reading health from 0x{:X} (pawn + 0x{:X})", health_addr, offsets::M_I_HEALTH);
        self.memory.read::<i32>(health_addr)
    }

    /// Get player team
    pub fn get_player_team(&mut self, pawn: usize) -> Result<i32, Box<dyn std::error::Error>> {
        if pawn == 0 {
            return Ok(0);
        }
        
        let team: u8 = self.memory.read(pawn + offsets::M_I_TEAM_NUM)?;
        Ok(team as i32)
    }

    /// Get player name from controller
    pub fn get_player_name(&mut self, controller: usize) -> Result<String, Box<dyn std::error::Error>> {
        if controller == 0 {
            return Ok("Unknown".to_string());
        }

        // Try to read the player name (this might need adjustment based on the actual structure)
        match self.memory.read_string(controller + offsets::M_S_SANITIZED_PLAYER_NAME, 64) {
            Ok(name) => {
                if name.is_empty() {
                    Ok("Player".to_string())
                } else {
                    Ok(name)
                }
            }
            Err(_) => Ok("Player".to_string()),
        }
    }

    /// Get all players in the game using the proper entity system
    pub fn get_all_players(&mut self) -> Result<Vec<Player>, Box<dyn std::error::Error>> {
        let mut players = Vec::new();

        println!("=== Scanning for all players using EntitySystem ===");

        // Get the highest entity index first
        let highest_index = self.entity_system.get_highest_entity_index()?;
        println!("Highest entity index: {}", highest_index);

        // Iterate through all entities manually to avoid borrowing issues
        for index in 0..=highest_index {
            if let Some(entity_ptr) = self.entity_system.get_entity_by_index(index)? {
                // Check if this entity looks like a player pawn by trying to read health
                match self.memory.read::<i32>(entity_ptr + offsets::M_I_HEALTH) {
                    Ok(health) if health > 0 && health <= 100 => {
                        println!("Found potential player at index {}: entity=0x{:X}, health={}", index, entity_ptr, health);

                        let position = self.get_player_position(entity_ptr).unwrap_or(Vector3::zero());
                        let team = self.get_player_team(entity_ptr).unwrap_or(0);

                        let player = Player {
                            controller: 0, // We don't have the controller for this method
                            pawn: entity_ptr,
                            position,
                            health,
                            team,
                            name: format!("Player{}", index),
                        };

                        players.push(player);
                    }
                    _ => {} // Not a valid player or couldn't read health
                }
            }
        }

        println!("Found {} players with health > 0", players.len());
        Ok(players)
    }

    /// Get the local player
    pub fn get_local_player(&mut self) -> Result<Option<Player>, Box<dyn std::error::Error>> {
        println!("=== Getting local player using EntitySystem ===");

        // Get the highest entity index first
        let highest_index = self.entity_system.get_highest_entity_index()?;
        println!("Highest entity index: {}", highest_index);

        // Iterate through entities to find the first valid player (assume it's the local player)
        let mut entities_found = 0;
        let mut entities_with_valid_ptr = 0;

        for index in 0..=highest_index {
            if let Some(entity_ptr) = self.entity_system.get_entity_by_index(index)? {
                entities_found += 1;
                entities_with_valid_ptr += 1;

                // Check if this entity looks like a player pawn by trying to read health
                match self.memory.read::<i32>(entity_ptr + offsets::M_I_HEALTH) {
                    Ok(health) => {
                        if index < 10 || (health > 0 && health <= 100) {
                            println!("Entity {}: ptr=0x{:X}, health={}", index, entity_ptr, health);
                        }

                        if health > 0 && health <= 100 {
                            println!("Found potential local player at index {}: entity=0x{:X}, health={}", index, entity_ptr, health);

                            let position = self.get_player_position(entity_ptr).unwrap_or(Vector3::zero());
                            let team = self.get_player_team(entity_ptr).unwrap_or(0);

                            let player = Player {
                                controller: 0, // We don't have the controller for this method
                                pawn: entity_ptr,
                                position,
                                health,
                                team,
                                name: "LocalPlayer".to_string(),
                            };

                            return Ok(Some(player));
                        }
                    }
                    Err(e) => {
                        if index < 10 {
                            println!("Entity {}: ptr=0x{:X}, failed to read health: {}", index, entity_ptr, e);
                        }
                    }
                }
            } else {
                entities_found += 1;
            }
        }

        println!("Scanned {} entities, {} had valid pointers", entities_found, entities_with_valid_ptr);

        Ok(None)
    }

    /// Get local player by scanning entities directly
    pub fn get_local_player_from_entity_scan(&mut self) -> Result<Option<Player>, Box<dyn std::error::Error>> {
        let entity_list = self.client_base + offsets::DW_ENTITY_LIST;
        let entity_chunk: usize = self.memory.read(entity_list)?;

        if entity_chunk == 0 {
            println!("  Entity chunk is null");
            return Ok(None);
        }

        // Scan through entity slots to find players
        for i in 0..512 {
            let entity_address = entity_chunk + (i * 120);
            let potential_pawn: usize = match self.memory.read(entity_address + 0x18) {
                Ok(p) => p,
                Err(_) => continue,
            };

            if potential_pawn == 0 || potential_pawn < 0x1000 || potential_pawn > 0x7FFFFFFFFFFF {
                continue;
            }

            let health = match self.memory.read::<i32>(potential_pawn + offsets::M_I_HEALTH) {
                Ok(h) => h,
                Err(_) => continue,
            };

            if health > 0 && health <= 100 {
                println!("  Found player at slot {}: pawn=0x{:X}, health={}", i, potential_pawn, health);

                let position = self.get_player_position(potential_pawn).unwrap_or(Vector3::zero());
                let team = self.get_player_team(potential_pawn).unwrap_or(0);

                let player = Player {
                    controller: 0,
                    pawn: potential_pawn,
                    position,
                    health,
                    team,
                    name: format!("Player{}", i),
                };

                // Return the first valid player we find (for now)
                return Ok(Some(player));
            }
        }

        Ok(None)
    }

    /// Old method (keeping for reference but not used)
    pub fn get_local_player_old(&mut self) -> Result<Option<Player>, Box<dyn std::error::Error>> {
        let controller = self.get_local_player_controller()?;
        if controller == 0 {
            return Ok(None);
        }

        // Use entity list method (direct pawn was giving wrong address)
        println!("  Using entity list method...");
        let pawn = self.get_player_pawn(controller)?;

        // If entity list method failed, try scanning for the local player in our entity scan
        if pawn == 0 || self.get_player_health(pawn).unwrap_or(0) == 0 {
            println!("  Entity list method failed, trying entity scan for local player...");
            let entity_list = self.client_base + offsets::DW_ENTITY_LIST;
            let entity_chunk: usize = self.memory.read(entity_list)?;

            if entity_chunk != 0 {
                // Scan through entity slots to find a player that might be us
                for i in 0..512 {
                    let entity_address = entity_chunk + (i * 120);
                    let potential_pawn: usize = match self.memory.read(entity_address + 0x18) {
                        Ok(p) => p,
                        Err(_) => continue,
                    };

                    if potential_pawn == 0 || potential_pawn < 0x1000 || potential_pawn > 0x7FFFFFFFFFFF {
                        continue;
                    }

                    let health = match self.memory.read::<i32>(potential_pawn + offsets::M_I_HEALTH) {
                        Ok(h) => h,
                        Err(_) => continue,
                    };

                    if health > 0 && health <= 100 {
                        println!("  Found potential local player at slot {}: pawn=0x{:X}, health={}", i, potential_pawn, health);
                        // Create a player object for this found pawn
                        let position = self.get_player_position(potential_pawn).unwrap_or(Vector3::zero());
                        let team = self.get_player_team(potential_pawn).unwrap_or(0);

                        let player = Player {
                            controller: 0, // We don't have the controller for this method
                            pawn: potential_pawn,
                            position,
                            health,
                            team,
                            name: "LocalPlayer".to_string(),
                        };

                        return Ok(Some(player));
                    }
                }
            }
        }

        if pawn == 0 {
            return Ok(None);
        }

        println!("  Using pawn address: 0x{:X}", pawn);

        // Validate pawn address is reasonable
        if pawn < 0x1000 || pawn > 0x7FFFFFFFFFFF {
            println!("  Pawn address looks invalid, skipping");
            return Ok(None);
        }

        // Test if we can read from the pawn address at all
        println!("  Testing pawn address accessibility...");
        match self.memory.read::<u32>(pawn) {
            Ok(test_value) => {
                println!("  Pawn address is readable, first 4 bytes: 0x{:X}", test_value);
            }
            Err(e) => {
                println!("  Cannot read from pawn address: {}", e);
                return Err(e);
            }
        }

        println!("  Reading player health...");
        let health = match self.get_player_health(pawn) {
            Ok(h) => {
                println!("  Health: {}", h);
                h
            }
            Err(e) => {
                println!("  Failed to read health: {}", e);
                return Err(e);
            }
        };

        println!("  Reading player position...");
        let position = match self.get_player_position(pawn) {
            Ok(p) => {
                println!("  Position: {}", p);
                p
            }
            Err(e) => {
                println!("  Failed to read position: {}", e);
                return Err(e);
            }
        };

        println!("  Reading player team...");
        let team = match self.get_player_team(pawn) {
            Ok(t) => {
                println!("  Team: {}", t);
                t
            }
            Err(e) => {
                println!("  Failed to read team: {}", e);
                return Err(e);
            }
        };

        println!("  Reading player name...");
        let name = match self.get_player_name(controller) {
            Ok(n) => {
                println!("  Name: {}", n);
                n
            }
            Err(e) => {
                println!("  Failed to read name: {}", e);
                return Err(e);
            }
        };

        let player = Player {
            controller,
            pawn,
            position,
            health,
            team,
            name,
        };

        if player.is_valid() {
            Ok(Some(player))
        } else {
            Ok(None)
        }
    }
}
