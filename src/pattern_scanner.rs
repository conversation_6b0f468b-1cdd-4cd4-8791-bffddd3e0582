use std::fs::File;
use std::io::{Read, Seek, SeekFrom};

/// Pattern scanner for finding byte patterns in memory
pub struct PatternScanner {
    memory_file: File,
}

/// Represents a code pattern with wildcards
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct CodePattern {
    pattern: Vec<Option<u8>>, // None represents wildcard (?)
    name: String,
}

/// Result of a pattern scan
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct PatternResult {
    pub address: usize,
    pub pattern_name: String,
}

impl CodePattern {
    /// Create a new pattern from a string like "48 81 EC ? ? ? ? 4C 8D 2D ? ? ? ? EB"
    pub fn new(pattern_str: &str, name: &str) -> Self {
        let mut pattern = Vec::new();
        
        for part in pattern_str.split_whitespace() {
            if part == "?" {
                pattern.push(None); // Wildcard
            } else {
                if let Ok(byte) = u8::from_str_radix(part, 16) {
                    pattern.push(Some(byte));
                } else {
                    panic!("Invalid hex byte in pattern: {}", part);
                }
            }
        }
        
        Self {
            pattern,
            name: name.to_string(),
        }
    }
    
    /// Add an offset to the found address
    pub fn add(self, offset: isize) -> PatternOffset {
        PatternOffset {
            pattern: self,
            offset,
            is_absolute: false,
            is_read: false,
        }
    }
    
    /// Get the length of the pattern
    pub fn len(&self) -> usize {
        self.pattern.len()
    }
    
    /// Check if pattern matches at given position in buffer
    pub fn matches_at(&self, buffer: &[u8], pos: usize) -> bool {
        if pos + self.pattern.len() > buffer.len() {
            return false;
        }
        
        for (i, &pattern_byte) in self.pattern.iter().enumerate() {
            if let Some(expected) = pattern_byte {
                if buffer[pos + i] != expected {
                    return false;
                }
            }
            // None (wildcard) always matches
        }
        
        true
    }
}

/// Pattern with offset calculations
#[derive(Debug, Clone)]
pub struct PatternOffset {
    pattern: CodePattern,
    offset: isize,
    is_absolute: bool,
    is_read: bool,
}

impl PatternOffset {
    /// Mark this as an absolute address calculation
    pub fn abs(mut self) -> Self {
        self.is_absolute = true;
        self
    }
    
    /// Mark this as a read operation (read value at address)
    pub fn read(mut self) -> Self {
        self.is_read = true;
        self
    }
    
    /// Get the final address after applying all transformations
    pub fn resolve(&self, base_address: usize, scanner: &mut PatternScanner) -> Result<usize, Box<dyn std::error::Error>> {
        // First find the pattern
        let pattern_addr = scanner.find_pattern(&self.pattern, base_address)?;
        
        // Apply offset
        let offset_addr = if self.offset >= 0 {
            pattern_addr + self.offset as usize
        } else {
            pattern_addr - (-self.offset) as usize
        };
        
        // Handle absolute address calculation
        let final_addr = if self.is_absolute {
            // Read a 32-bit relative offset and calculate absolute address
            let relative_offset: i32 = scanner.read_at(offset_addr)?;
            let instruction_end = offset_addr + 4; // 4 bytes for the i32
            (instruction_end as i64 + relative_offset as i64) as usize
        } else {
            offset_addr
        };
        
        // Handle read operation
        if self.is_read {
            // Read the value at the address
            scanner.read_at(final_addr)
        } else {
            Ok(final_addr)
        }
    }
}

impl PatternScanner {
    /// Create a new pattern scanner for a process
    pub fn new(pid: u32) -> Result<Self, Box<dyn std::error::Error>> {
        let mem_path = format!("/proc/{}/mem", pid);
        let memory_file = File::open(mem_path)?;
        
        Ok(Self { memory_file })
    }
    
    /// Find a pattern in memory starting from base_address
    pub fn find_pattern(&mut self, pattern: &CodePattern, base_address: usize) -> Result<usize, Box<dyn std::error::Error>> {
        // Read a large chunk of memory to search in
        const SEARCH_SIZE: usize = 0x1000000; // 16MB search window
        let mut buffer = vec![0u8; SEARCH_SIZE];
        
        self.memory_file.seek(SeekFrom::Start(base_address as u64))?;
        let bytes_read = self.memory_file.read(&mut buffer)?;
        buffer.truncate(bytes_read);
        
        // Search for the pattern
        for i in 0..buffer.len() {
            if pattern.matches_at(&buffer, i) {
                println!("Found pattern '{}' at offset 0x{:X} (absolute: 0x{:X})", 
                         pattern.name, i, base_address + i);
                return Ok(base_address + i);
            }
        }
        
        Err(format!("Pattern '{}' not found", pattern.name).into())
    }
    
    /// Read a value at a specific address
    pub fn read_at<T>(&mut self, address: usize) -> Result<T, Box<dyn std::error::Error>>
    where
        T: Copy + Default,
    {
        let size = std::mem::size_of::<T>();
        let mut buffer = vec![0u8; size];
        
        self.memory_file.seek(SeekFrom::Start(address as u64))?;
        self.memory_file.read_exact(&mut buffer)?;
        
        // Safety: We know the size matches and T is Copy
        let result = unsafe { std::ptr::read(buffer.as_ptr() as *const T) };
        Ok(result)
    }
}

/// Linux-specific patterns from Osiris
pub struct LinuxPatterns;

impl LinuxPatterns {
    /// EntitySystemPointer pattern
    pub fn entity_system_pointer() -> PatternOffset {
        CodePattern::new("48 81 EC ? ? ? ? 4C 8D 2D ? ? ? ? EB", "EntitySystemPointer")
            .add(10)
            .abs()
    }
    
    /// HighestEntityIndexOffset pattern  
    pub fn highest_entity_index_offset() -> PatternOffset {
        CodePattern::new("39 97 ? ? ? ? 7D 06", "HighestEntityIndexOffset")
            .add(2)
            .read()
    }
    
    /// EntityListOffset pattern - try multiple variations
    pub fn entity_list_offset() -> PatternOffset {
        CodePattern::new("4C 8D 6F ? 41 54 53 48 89 FB 48 83 EC ? 48 89 07 48", "EntityListOffset")
            .add(3)
            .read()
    }

    /// Alternative EntityListOffset pattern
    pub fn entity_list_offset_alt() -> PatternOffset {
        CodePattern::new("48 8D 05 ? ? ? ? 48 89 45 ? 48 8D 05", "EntityListOffsetAlt")
            .add(3)
            .read()
    }

    /// Another alternative EntityListOffset pattern
    pub fn entity_list_offset_alt2() -> PatternOffset {
        CodePattern::new("48 8B 05 ? ? ? ? 48 8B 04 D0", "EntityListOffsetAlt2")
            .add(3)
            .read()
    }
    
    /// OffsetToGameSceneNode pattern (from EntityPatternsLinux.h)
    pub fn offset_to_game_scene_node() -> PatternOffset {
        CodePattern::new("E8 ? ? ? ? 84 C0 75 ? 49 8B BC 24 ? ? ? ? 4C 89 EE", "OffsetToGameSceneNode")
            .add(13)
            .read()
    }

    /// OffsetToHealth pattern (from EntityPatternsLinux.h)
    pub fn offset_to_health() -> PatternOffset {
        CodePattern::new("C7 87 ? ? ? ? 00 00 00 00 48 8D 35", "OffsetToHealth")
            .add(2)
            .read()
    }

    /// OffsetToTeamNumber pattern (from EntityPatternsLinux.h)
    pub fn offset_to_team_number() -> PatternOffset {
        CodePattern::new("41 0F B6 84 24 ? ? ? ? 3C 03", "OffsetToTeamNumber")
            .add(5)
            .read()
    }

    /// OffsetToAbsOrigin pattern (from GameSceneNodePatternsLinux.h)
    pub fn offset_to_abs_origin() -> PatternOffset {
        CodePattern::new("43 ? F3 0F 10 83 ? ? ? ? 66", "OffsetToAbsOrigin")
            .add(6)
            .read()
    }
}
