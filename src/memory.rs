use std::fs::File;
use std::io::{Read, Seek, SeekFrom};
use std::mem;
use sysinfo::System;
use nix::sys::uio::{process_vm_readv, RemoteIoVec};
use nix::unistd::Pid;

/// Memory reader for external process memory access
pub struct MemoryReader {
    pub pid: u32,
    mem_file: Option<File>,
}

impl MemoryReader {
    /// Create a new memory reader for the given process ID
    pub fn new(pid: u32) -> Result<Self, Box<dyn std::error::Error>> {
        let mem_path = format!("/proc/{}/mem", pid);
        let mem_file = match File::open(&mem_path) {
            Ok(file) => Some(file),
            Err(e) => {
                eprintln!("Warning: Could not open {}: {}. Memory reading may fail.", mem_path, e);
                None
            }
        };

        Ok(MemoryReader { pid, mem_file })
    }

    /// Find CS2 process by name
    pub fn find_cs2_process() -> Option<u32> {
        let mut system = System::new_all();
        system.refresh_all();

        // Look for the actual CS2 binary (not shell scripts)
        for (pid, process) in system.processes() {
            let name = process.name().to_string_lossy();
            let exe_path = process.exe().map(|p| p.to_string_lossy().to_string()).unwrap_or_default();

            // Look for the actual cs2 binary, not shell scripts
            if (name == "cs2" && !exe_path.contains(".sh")) ||
               (exe_path.contains("/cs2") && exe_path.contains("bin") && !exe_path.contains(".sh")) {
                println!("Found CS2 process: {} (PID: {}) at {}", name, pid, exe_path);
                return Some(pid.as_u32());
            }
        }

        // Fallback: look for any process with cs2 in the path (but not shell scripts)
        for (pid, process) in system.processes() {
            let exe_path = process.exe().map(|p| p.to_string_lossy().to_string()).unwrap_or_default();
            if exe_path.contains("cs2") && exe_path.contains("bin") && !exe_path.contains(".sh") {
                println!("Found potential CS2 process: {} (PID: {}) at {}", process.name().to_string_lossy(), pid, exe_path);
                return Some(pid.as_u32());
            }
        }

        None
    }

    /// Read a value of type T from the given memory address
    pub fn read<T: Copy>(&mut self, address: usize) -> Result<T, Box<dyn std::error::Error>> {
        // Try process_vm_readv first (more reliable)
        let mut buffer = vec![0u8; mem::size_of::<T>()];

        match self.read_vm(&mut buffer, address) {
            Ok(_) => {
                // Safety: We're reading exactly the size of T and T is Copy
                let value = unsafe { std::ptr::read(buffer.as_ptr() as *const T) };
                Ok(value)
            }
            Err(_) => {
                // Fallback to /proc/pid/mem
                if let Some(ref mut file) = self.mem_file {
                    file.seek(SeekFrom::Start(address as u64))?;
                    file.read_exact(&mut buffer)?;

                    // Safety: We're reading exactly the size of T and T is Copy
                    let value = unsafe { std::ptr::read(buffer.as_ptr() as *const T) };
                    Ok(value)
                } else {
                    Err("Both memory reading methods failed".into())
                }
            }
        }
    }

    /// Read memory using process_vm_readv
    fn read_vm(&self, buffer: &mut [u8], address: usize) -> Result<(), Box<dyn std::error::Error>> {
        use nix::unistd::Pid;

        let buffer_len = buffer.len();
        let local_iov = std::io::IoSliceMut::new(buffer);
        let remote_iov = RemoteIoVec {
            base: address,
            len: buffer_len,
        };

        let pid = Pid::from_raw(self.pid as i32);
        let bytes_read = process_vm_readv(pid, &mut [local_iov], &[remote_iov])?;

        if bytes_read != buffer_len {
            return Err("Incomplete read".into());
        }

        Ok(())
    }

    /// Read a buffer of bytes from memory
    pub fn read_bytes(&mut self, address: usize, size: usize) -> Result<Vec<u8>, Box<dyn std::error::Error>> {
        if let Some(ref mut file) = self.mem_file {
            file.seek(SeekFrom::Start(address as u64))?;
            
            let mut buffer = vec![0u8; size];
            file.read_exact(&mut buffer)?;
            Ok(buffer)
        } else {
            Err("Memory file not available".into())
        }
    }

    /// Read a null-terminated string from memory
    pub fn read_string(&mut self, address: usize, max_length: usize) -> Result<String, Box<dyn std::error::Error>> {
        let bytes = self.read_bytes(address, max_length)?;
        
        // Find the null terminator
        let end = bytes.iter().position(|&b| b == 0).unwrap_or(bytes.len());
        let string_bytes = &bytes[..end];
        
        Ok(String::from_utf8_lossy(string_bytes).to_string())
    }
}

/// Vector3 structure for 3D positions
#[derive(Debug, Clone, Copy)]
#[repr(C)]
pub struct Vector3 {
    pub x: f32,
    pub y: f32,
    pub z: f32,
}

impl Vector3 {
    pub fn new(x: f32, y: f32, z: f32) -> Self {
        Self { x, y, z }
    }

    pub fn zero() -> Self {
        Self::new(0.0, 0.0, 0.0)
    }

    pub fn distance_to(&self, other: &Vector3) -> f32 {
        let dx = self.x - other.x;
        let dy = self.y - other.y;
        let dz = self.z - other.z;
        (dx * dx + dy * dy + dz * dz).sqrt()
    }
}

impl std::fmt::Display for Vector3 {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "({:.2}, {:.2}, {:.2})", self.x, self.y, self.z)
    }
}

/// Entity handle structure (matches CS2's CEntityHandle)
#[derive(Debug, Clone, Copy)]
#[repr(C)]
pub struct EntityHandle {
    pub value: u32,
}

impl EntityHandle {
    pub fn new(value: u32) -> Self {
        Self { value }
    }

    pub fn index(&self) -> i32 {
        (self.value & 0x7FFF) as i32
    }

    pub fn is_valid(&self) -> bool {
        let index = self.index();
        index >= 0 && index <= 0x7FFE
    }
}

/// Entity identity structure (matches CS2's CEntityIdentity)
#[derive(Debug)]
#[repr(C)]
pub struct EntityIdentity {
    pub entity: usize,        // CEntityInstance*
    pub entity_class: usize,  // CEntityClass*
    pub handle: EntityHandle, // CEntityHandle
    pub _pad: [u8; 100],      // Padding to make it 120 bytes total
}

/// Entity system for proper entity enumeration
pub struct EntitySystem {
    memory: MemoryReader,
    game_entity_system: usize,
    entity_list: usize,
}

impl EntitySystem {
    pub fn new(mut memory: MemoryReader, client_base: usize) -> Result<Self, Box<dyn std::error::Error>> {
        // Get the game entity system pointer
        let game_entity_system_ptr = client_base + offsets::DW_GAME_ENTITY_SYSTEM;
        let game_entity_system: usize = memory.read(game_entity_system_ptr)?;

        if game_entity_system == 0 {
            return Err("Game entity system is null".into());
        }

        // Get the entity list directly from client base (not from game entity system)
        let entity_list_ptr = client_base + offsets::DW_ENTITY_LIST;
        let entity_list: usize = memory.read(entity_list_ptr)?;

        if entity_list == 0 {
            return Err("Entity list is null".into());
        }

        println!("EntitySystem initialized:");
        println!("  Game Entity System: 0x{:X}", game_entity_system);
        println!("  Entity List: 0x{:X}", entity_list);

        Ok(EntitySystem {
            memory,
            game_entity_system,
            entity_list,
        })
    }

    /// Get the highest entity index
    pub fn get_highest_entity_index(&mut self) -> Result<i32, Box<dyn std::error::Error>> {
        let highest_index: i32 = self.memory.read(self.game_entity_system + offsets::DW_GAME_ENTITY_SYSTEM_HIGHEST_ENTITY_INDEX)?;
        Ok(highest_index)
    }

    /// Get entity by index using the proper chunked system
    pub fn get_entity_by_index(&mut self, index: i32) -> Result<Option<usize>, Box<dyn std::error::Error>> {
        if index < 0 || index > 0x7FFE {
            return Ok(None);
        }

        // Calculate chunk index and index within chunk
        let chunk_index = (index as usize) / offsets::ENTITIES_PER_CHUNK;
        let index_in_chunk = (index as usize) % offsets::ENTITIES_PER_CHUNK;

        if chunk_index >= offsets::MAX_CHUNKS {
            return Ok(None);
        }

        // Read the chunk pointer
        let chunk_ptr_addr = self.entity_list + (chunk_index * 8);
        let chunk_ptr: usize = match self.memory.read(chunk_ptr_addr) {
            Ok(ptr) => ptr,
            Err(e) => {
                if index < 5 {
                    println!("Failed to read chunk {} pointer at 0x{:X}: {}", chunk_index, chunk_ptr_addr, e);
                }
                return Ok(None);
            }
        };

        if chunk_ptr == 0 {
            if index < 5 {
                println!("Chunk {} pointer is null", chunk_index);
            }
            return Ok(None);
        }

        if index < 5 {
            println!("Chunk {}: ptr=0x{:X}", chunk_index, chunk_ptr);
        }

        // Calculate the entity identity address
        let entity_identity_addr = chunk_ptr + (index_in_chunk * offsets::ENTITY_IDENTITY_SIZE);

        // Read the entity identity
        let entity_ptr: usize = match self.memory.read(entity_identity_addr) {
            Ok(ptr) => ptr,
            Err(e) => {
                if index < 5 {
                    println!("Failed to read entity {} identity at 0x{:X}: {}", index, entity_identity_addr, e);
                }
                return Ok(None);
            }
        };

        if index < 5 {
            println!("Entity {}: identity_addr=0x{:X}, entity_ptr=0x{:X}", index, entity_identity_addr, entity_ptr);
        }

        if entity_ptr == 0 {
            return Ok(None);
        }

        // TODO: Verify the handle matches (skipping for now to test)
        // let handle_addr = entity_identity_addr + 16; // handle is at offset 16 in EntityIdentity
        // let handle: EntityHandle = match self.memory.read(handle_addr) {
        //     Ok(h) => h,
        //     Err(_) => return Ok(None),
        // };
        //
        // if handle.index() != index {
        //     return Ok(None);
        // }

        Ok(Some(entity_ptr))
    }

    /// Iterate through all valid entities
    pub fn for_each_entity<F>(&mut self, mut callback: F) -> Result<(), Box<dyn std::error::Error>>
    where
        F: FnMut(i32, usize) -> Result<bool, Box<dyn std::error::Error>>, // (index, entity_ptr) -> continue?
    {
        let highest_index = self.get_highest_entity_index()?;

        for i in 0..=highest_index {
            if let Some(entity_ptr) = self.get_entity_by_index(i)? {
                if !callback(i, entity_ptr)? {
                    break;
                }
            }
        }

        Ok(())
    }
}

/// CS2 Player structure
#[derive(Debug)]
pub struct Player {
    pub controller: usize,
    pub pawn: usize,
    pub position: Vector3,
    pub health: i32,
    pub team: i32,
    pub name: String,
}

impl Player {
    pub fn new() -> Self {
        Self {
            controller: 0,
            pawn: 0,
            position: Vector3::zero(),
            health: 0,
            team: 0,
            name: String::new(),
        }
    }

    pub fn is_valid(&self) -> bool {
        self.pawn != 0 && self.health > 0
    }

    pub fn is_alive(&self) -> bool {
        self.health > 0
    }
}

/// CS2 Memory offsets (updated with fresh Linux offsets from cs2-dumper)
pub mod offsets {
    // From offsets.rs - Fresh Linux offsets
    pub const DW_LOCAL_PLAYER_CONTROLLER: usize = 0x3946FF8;
    pub const DW_LOCAL_PLAYER_PAWN: usize = 0x3966DC8;
    pub const DW_ENTITY_LIST: usize = 0x37B5180;
    pub const DW_GAME_ENTITY_SYSTEM: usize = 0x3AC9F38;
    pub const DW_GAME_ENTITY_SYSTEM_HIGHEST_ENTITY_INDEX: usize = 0x2110;

    // Player controller offsets (from CCSPlayerController)
    pub const M_H_PLAYER_PAWN: usize = 0x9AC; // CHandle<C_CSPlayerPawn>
    pub const M_I_PAWN_HEALTH: usize = 0x9B8; // uint32 - health stored in controller
    pub const M_S_SANITIZED_PLAYER_NAME: usize = 0x900; // CUtlString

    // Player pawn offsets (from C_BaseEntity)
    pub const M_I_HEALTH: usize = 0x4BC; // int32
    pub const M_I_TEAM_NUM: usize = 0x55B; // uint8
    pub const M_P_GAME_SCENE_NODE: usize = 0x4A0; // CGameSceneNode*

    // Game scene node offsets (from CGameSceneNode)
    pub const M_VEC_ORIGIN: usize = 0x88; // CNetworkOriginCellCoordQuantizedVector
    pub const M_VEC_ABS_ORIGIN: usize = 0xD0; // Vector

    // Entity system constants
    pub const ENTITIES_PER_CHUNK: usize = 512;
    pub const MAX_CHUNKS: usize = 64;
    pub const ENTITY_IDENTITY_SIZE: usize = 120;
}
